import { AssetType, CustomField, LifecycleStage, MaintenanceSchedule, DepreciationSettings, AutomatedAction } from "@/lib/modules/asset-types/types";

export interface AssetTypeTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  icon: string;
  color: string;
  tags: string[];
  assetType: Partial<AssetType>;
  customFields: Omit<CustomField, "id">[];
  lifecycleStages: Omit<LifecycleStage, "id">[];
  maintenanceSchedules: Omit<MaintenanceSchedule, "id">[];
  depreciationSettings: DepreciationSettings;
  formTemplates: {
    operationType: string;
    sections: any[];
    settings: any;
  }[];
}

export const ASSET_TYPE_TEMPLATES: Record<string, AssetTypeTemplate> = {
  "laptop-computer": {
    id: "laptop-computer",
    name: "Laptop Computer",
    description: "Standard laptop computer template with IT-specific configurations",
    category: "IT Equipment",
    icon: "Laptop",
    color: "#3B82F6",
    tags: ["IT", "Computing", "Portable"],
    assetType: {
      name: "Laptop Computer",
      code: "LAPTOP",
      description: "Portable computers for employees",
      subcategory: "Computing Devices",
    },
    customFields: [
      {
        name: "processor",
        label: "Processor",
        type: "text",
        description: "CPU model and specifications",
        isRequired: true,
        isUnique: false,
        validation: { minLength: 2, maxLength: 100 },
        displayOrder: 1,
        groupName: "Hardware Specifications",
        isActive: true,
      },
      {
        name: "memory",
        label: "Memory (RAM)",
        type: "select",
        description: "System memory in GB",
        isRequired: true,
        isUnique: false,
        options: [
          { value: "4GB", label: "4GB", isActive: true },
          { value: "8GB", label: "8GB", isActive: true },
          { value: "16GB", label: "16GB", isActive: true },
          { value: "32GB", label: "32GB", isActive: true },
          { value: "64GB", label: "64GB", isActive: true },
        ],
        displayOrder: 2,
        groupName: "Hardware Specifications",
        isActive: true,
      },
      {
        name: "storage",
        label: "Storage",
        type: "text",
        description: "Storage type and capacity",
        isRequired: true,
        isUnique: false,
        validation: { minLength: 2, maxLength: 50 },
        displayOrder: 3,
        groupName: "Hardware Specifications",
        isActive: true,
      },
      {
        name: "operatingSystem",
        label: "Operating System",
        type: "select",
        description: "Installed operating system",
        isRequired: true,
        isUnique: false,
        options: [
          { value: "Windows 11", label: "Windows 11", isActive: true },
          { value: "Windows 10", label: "Windows 10", isActive: true },
          { value: "macOS", label: "macOS", isActive: true },
          { value: "Ubuntu", label: "Ubuntu", isActive: true },
          { value: "Other", label: "Other", isActive: true },
        ],
        displayOrder: 4,
        groupName: "Software",
        isActive: true,
      },
      {
        name: "warrantyExpiry",
        label: "Warranty Expiry",
        type: "date",
        description: "Warranty expiration date",
        isRequired: false,
        isUnique: false,
        validation: {},
        displayOrder: 5,
        groupName: "Warranty",
        isActive: true,
      },
      {
        name: "assignedUser",
        label: "Assigned User",
        type: "user_select",
        description: "Employee assigned to this laptop",
        isRequired: false,
        isUnique: false,
        validation: {},
        displayOrder: 6,
        groupName: "Assignment",
        isActive: true,
      },
    ],
    lifecycleStages: [
      {
        name: "Requested",
        code: "REQUESTED",
        description: "Laptop has been requested but not yet ordered",
        order: 1,
        isInitial: true,
        isFinal: false,
        color: "#F59E0B",
        icon: "Clock",
        allowedTransitions: ["ORDERED", "CANCELLED"],
        requiredFields: ["requestedBy", "justification"],
        automatedActions: [] as AutomatedAction[],
        notifications: [],
        isActive: true,
      },
      {
        name: "Ordered",
        code: "ORDERED",
        description: "Laptop has been ordered from supplier",
        order: 2,
        isInitial: false,
        isFinal: false,
        color: "#3B82F6",
        icon: "ShoppingCart",
        allowedTransitions: ["RECEIVED", "CANCELLED"],
        requiredFields: ["supplier", "orderDate", "expectedDelivery"],
        automatedActions: [] as AutomatedAction[],
        notifications: [],
        isActive: true,
      },
      {
        name: "Received",
        code: "RECEIVED",
        description: "Laptop has been received and is being configured",
        order: 3,
        isInitial: false,
        isFinal: false,
        color: "#10B981",
        icon: "Package",
        allowedTransitions: ["CONFIGURED", "DEFECTIVE"],
        requiredFields: ["receivedDate", "condition", "serialNumber"],
        automatedActions: [
          { type: "generate_asset_tag" },
          {
            id: "create_inventory_record",
            name: "Create Inventory Record",
            type: "integration",
            trigger: { event: "stage_enter" },
            conditions: [],
            actions: [],
            isActive: true
          }
        ] as AutomatedAction[],
        notifications: ["notify_it_team"],
        isActive: true,
      },
      {
        name: "Configured",
        code: "CONFIGURED",
        description: "Laptop has been configured and is ready for deployment",
        order: 4,
        isInitial: false,
        isFinal: false,
        color: "#8B5CF6",
        icon: "Settings",
        allowedTransitions: ["DEPLOYED", "IN_STORAGE"],
        requiredFields: ["configuredBy", "configurationDate", "softwareInstalled"],
        automatedActions: [
          { type: "update_inventory_status" }
        ] as AutomatedAction[],
        notifications: [],
        isActive: true,
      },
      {
        name: "Deployed",
        code: "DEPLOYED",
        description: "Laptop is deployed and in active use",
        order: 5,
        isInitial: false,
        isFinal: false,
        color: "#059669",
        icon: "Play",
        allowedTransitions: ["MAINTENANCE", "RETURNED", "DISPOSED"],
        requiredFields: ["assignedTo", "deploymentDate", "location"],
        automatedActions: [
          { type: "schedule_maintenance" },
          { type: "notify_user" }
        ] as AutomatedAction[],
        notifications: ["deployment_confirmation"],
        isActive: true,
      },
      {
        name: "Maintenance",
        code: "MAINTENANCE",
        description: "Laptop is under maintenance or repair",
        order: 6,
        isInitial: false,
        isFinal: false,
        color: "#F59E0B",
        icon: "Wrench",
        allowedTransitions: ["DEPLOYED", "DISPOSED", "DEFECTIVE"],
        requiredFields: ["maintenanceType", "scheduledDate", "technician"],
        automatedActions: [
          { type: "notify_user_maintenance" }
        ] as AutomatedAction[],
        notifications: ["maintenance_started", "maintenance_completed"],
        isActive: true,
      },
      {
        name: "Returned",
        code: "RETURNED",
        description: "Laptop has been returned by user",
        order: 7,
        isInitial: false,
        isFinal: false,
        color: "#6B7280",
        icon: "RotateCcw",
        allowedTransitions: ["CONFIGURED", "DISPOSED", "IN_STORAGE"],
        requiredFields: ["returnDate", "returnReason", "condition"],
        automatedActions: [
          { type: "data_wipe_required" }
        ] as AutomatedAction[],
        notifications: ["return_processed"],
        isActive: true,
      },
      {
        name: "Disposed",
        code: "DISPOSED",
        description: "Laptop has been disposed of or recycled",
        order: 8,
        isInitial: false,
        isFinal: true,
        color: "#EF4444",
        icon: "Trash2",
        allowedTransitions: [],
        requiredFields: ["disposalMethod", "disposalDate", "dataWipeConfirmed"],
        automatedActions: [
          { type: "remove_from_inventory" },
          { type: "update_financial_records" }
        ] as AutomatedAction[],
        notifications: ["disposal_completed"],
        isActive: true,
      },
    ],
    maintenanceSchedules: [
      {
        name: "Software Updates",
        description: "Regular software and security updates",
        type: "preventive",
        frequency: { type: "months", interval: 1 },
        priority: "medium",
        estimatedDuration: 30,
        estimatedCost: 0,
        requiredSkills: ["IT Support"],
        instructions: "Run all available software updates and security patches. Restart if required.",
        checklistItems: [
          "Check for OS updates",
          "Update antivirus software",
          "Update installed applications",
          "Clear temporary files",
          "Restart system if required",
          "Verify system functionality",
        ],
        triggers: [
          { type: "time_based", value: "monthly" },
          { type: "usage_based", value: "high_usage" },
        ],
        isActive: true,
      },
      {
        name: "Hardware Inspection",
        description: "Physical inspection of laptop hardware components",
        type: "preventive",
        frequency: { type: "months", interval: 6 },
        priority: "low",
        estimatedDuration: 15,
        estimatedCost: 0,
        requiredSkills: ["IT Support"],
        instructions: "Perform visual and functional inspection of laptop hardware components.",
        checklistItems: [
          "Check screen for damage or dead pixels",
          "Test keyboard functionality",
          "Test trackpad/mouse functionality",
          "Inspect ports and connections",
          "Check battery health and charging",
          "Verify fan operation and cooling",
          "Clean vents and keyboard",
        ],
        triggers: [
          { type: "time_based", value: "semi_annual" },
        ],
        isActive: true,
      },
      {
        name: "Performance Optimization",
        description: "System performance analysis and optimization",
        type: "predictive",
        frequency: { type: "months", interval: 3 },
        priority: "medium",
        estimatedDuration: 45,
        estimatedCost: 0,
        requiredSkills: ["IT Support", "System Administrator"],
        instructions: "Analyze system performance and optimize for better efficiency.",
        checklistItems: [
          "Run disk cleanup and defragmentation",
          "Check startup programs",
          "Analyze system performance metrics",
          "Clear browser cache and data",
          "Update device drivers",
          "Check for malware",
          "Optimize power settings",
        ],
        triggers: [
          { type: "performance_based", value: "slow_performance" },
          { type: "time_based", value: "quarterly" },
        ],
        isActive: true,
      },
    ],
    depreciationSettings: {
      method: "double_declining_balance",
      usefulLife: 3,
      usefulLifeUnit: "years",
      salvageValue: 10,
      salvageValueType: "percentage",
      startDate: new Date().toISOString(),
      isActive: true,
    },
    formTemplates: [
      {
        operationType: "asset.create",
        sections: [
          {
            id: "basic-info",
            title: "Basic Information",
            description: "Essential laptop details",
            columns: 2,
            fields: ["name", "serialNumber", "model", "manufacturer"],
          },
          {
            id: "hardware-specs",
            title: "Hardware Specifications",
            description: "Technical specifications",
            columns: 2,
            fields: ["processor", "memory", "storage", "operatingSystem"],
          },
          {
            id: "purchase-info",
            title: "Purchase Information",
            description: "Financial and supplier details",
            columns: 2,
            fields: ["purchasePrice", "purchaseDate", "supplier", "warrantyExpiry"],
          },
          {
            id: "assignment",
            title: "Assignment",
            description: "User and location assignment",
            columns: 2,
            fields: ["assignedUser", "location", "department"],
          },
        ],
        settings: {
          layout: "standard",
          labelPosition: "top",
          submitButtonText: "Create Laptop",
          cancelButtonText: "Cancel",
          showProgressBar: true,
          allowSaveAsDraft: true,
          requiredFieldsIndicator: "*",
        },
      },
      {
        operationType: "asset.transfer",
        sections: [
          {
            id: "transfer-details",
            title: "Transfer Details",
            description: "New assignment information",
            columns: 1,
            fields: ["newAssignedUser", "newLocation", "newDepartment", "transferDate", "transferReason"],
          },
          {
            id: "condition-check",
            title: "Condition Assessment",
            description: "Current condition of the laptop",
            columns: 2,
            fields: ["physicalCondition", "functionalStatus", "accessories", "notes"],
          },
        ],
        settings: {
          layout: "standard",
          labelPosition: "top",
          submitButtonText: "Transfer Laptop",
          cancelButtonText: "Cancel",
          showProgressBar: false,
          allowSaveAsDraft: false,
        },
      },
      {
        operationType: "maintenance.log",
        sections: [
          {
            id: "maintenance-details",
            title: "Maintenance Details",
            description: "Maintenance activity information",
            columns: 2,
            fields: ["maintenanceType", "completedDate", "performedBy", "duration"],
          },
          {
            id: "work-performed",
            title: "Work Performed",
            description: "Details of maintenance work",
            columns: 1,
            fields: ["workDescription", "partsReplaced", "softwareUpdated", "issuesFound"],
          },
          {
            id: "completion",
            title: "Completion Status",
            description: "Final status and notes",
            columns: 2,
            fields: ["completionStatus", "nextMaintenanceDate", "recommendations", "notes"],
          },
        ],
        settings: {
          layout: "standard",
          labelPosition: "top",
          submitButtonText: "Log Maintenance",
          cancelButtonText: "Cancel",
          showProgressBar: false,
          allowSaveAsDraft: true,
        },
      },
    ],
  },

  "office-desk": {
    id: "office-desk",
    name: "Office Desk",
    description: "Standard office desk template with furniture-specific configurations",
    category: "Office Furniture",
    icon: "Table",
    color: "#8B5CF6",
    tags: ["Furniture", "Office", "Workspace"],
    assetType: {
      name: "Office Desk",
      code: "DESK",
      description: "Standard office desks for workspaces",
      subcategory: "Furniture",
    },
    customFields: [
      {
        name: "dimensions",
        label: "Dimensions (L x W x H)",
        type: "text",
        description: "Desk dimensions in cm",
        isRequired: true,
        isUnique: false,
        validation: { pattern: "^\\d+x\\d+x\\d+$" },
        displayOrder: 1,
        groupName: "Physical Specifications",
        isActive: true,
      },
      {
        name: "material",
        label: "Material",
        type: "select",
        description: "Primary desk material",
        isRequired: true,
        isUnique: false,
        validation: {},
        options: [
          { value: "Wood", label: "Wood", isActive: true },
          { value: "Metal", label: "Metal", isActive: true },
          { value: "Glass", label: "Glass", isActive: true },
          { value: "Laminate", label: "Laminate", isActive: true },
          { value: "Composite", label: "Composite", isActive: true },
        ],
        displayOrder: 2,
        groupName: "Physical Specifications",
        isActive: true,
      },
      {
        name: "color",
        label: "Color",
        type: "text",
        description: "Desk color or finish",
        isRequired: false,
        isUnique: false,
        validation: {},
        displayOrder: 3,
        groupName: "Physical Specifications",
        isActive: true,
      },
      {
        name: "hasDrawers",
        label: "Has Drawers",
        type: "boolean",
        description: "Whether the desk has drawers",
        isRequired: false,
        isUnique: false,
        validation: {},
        displayOrder: 4,
        groupName: "Features",
        isActive: true,
      },
      {
        name: "drawerCount",
        label: "Number of Drawers",
        type: "number",
        description: "Total number of drawers",
        isRequired: false,
        isUnique: false,
        validation: {},
        conditionalLogic: [
          {
            condition: {
              fieldId: "hasDrawers",
              operator: "equals",
              value: true,
            },
            action: {
              type: "show",
            },
            targetFieldId: "drawerCount",
          },
        ],
        displayOrder: 5,
        groupName: "Features",
        isActive: true,
      },
      {
        name: "isAdjustable",
        label: "Height Adjustable",
        type: "boolean",
        description: "Whether the desk height is adjustable",
        isRequired: false,
        isUnique: false,
        validation: {},
        displayOrder: 6,
        groupName: "Features",
        isActive: true,
      },
    ],
    lifecycleStages: [
      {
        name: "Ordered",
        code: "ORDERED",
        description: "Desk has been ordered from supplier",
        order: 1,
        isInitial: true,
        isFinal: false,
        color: "#3B82F6",
        icon: "ShoppingCart",
        allowedTransitions: ["RECEIVED", "CANCELLED"],
        requiredFields: ["supplier", "orderDate"],
        automatedActions: [] as AutomatedAction[],
        notifications: [],
        isActive: true,
      },
      {
        name: "Received",
        code: "RECEIVED",
        description: "Desk has been received and inspected",
        order: 2,
        isInitial: false,
        isFinal: false,
        color: "#10B981",
        icon: "Package",
        allowedTransitions: ["INSTALLED", "DAMAGED"],
        requiredFields: ["receivedDate", "condition"],
        automatedActions: [
          { type: "create_inventory_record", id: "1", name: "Create Inventory Record", trigger: "on_create", conditions: [], actions: [] } as AutomatedAction
        ] as AutomatedAction[],
        notifications: ["notify_facilities_team"],
        isActive: true,
      },
      {
        name: "Installed",
        code: "INSTALLED",
        description: "Desk has been installed and is ready for use",
        order: 3,
        isInitial: false,
        isFinal: false,
        color: "#059669",
        icon: "CheckCircle",
        allowedTransitions: ["IN_USE", "IN_STORAGE"],
        requiredFields: ["installationDate", "location"],
        automatedActions: [
          { type: "update_space_allocation" }
        ] as AutomatedAction[],
        notifications: [],
        isActive: true,
      },
      {
        name: "In Use",
        code: "IN_USE",
        description: "Desk is actively being used",
        order: 4,
        isInitial: false,
        isFinal: false,
        color: "#059669",
        icon: "Play",
        allowedTransitions: ["MAINTENANCE", "RELOCATED", "DISPOSED"],
        requiredFields: ["assignedTo", "workspaceId"],
        automatedActions: [] as AutomatedAction[],
        notifications: [],
        isActive: true,
      },
      {
        name: "Maintenance",
        code: "MAINTENANCE",
        description: "Desk is under maintenance or repair",
        order: 5,
        isInitial: false,
        isFinal: false,
        color: "#F59E0B",
        icon: "Wrench",
        allowedTransitions: ["IN_USE", "DISPOSED"],
        requiredFields: ["maintenanceType", "scheduledDate"],
        automatedActions: [] as AutomatedAction[],
        notifications: ["maintenance_scheduled"],
        isActive: true,
      },
      {
        name: "Disposed",
        code: "DISPOSED",
        description: "Desk has been disposed of or recycled",
        order: 6,
        isInitial: false,
        isFinal: true,
        color: "#EF4444",
        icon: "Trash2",
        allowedTransitions: [],
        requiredFields: ["disposalMethod", "disposalDate"],
        automatedActions: [
          { type: "update_space_allocation" },
          { type: "remove_from_inventory" }
        ] as AutomatedAction[],
        notifications: ["disposal_completed"],
        isActive: true,
      },
    ],
    maintenanceSchedules: [
      {
        name: "Annual Inspection",
        description: "Annual inspection of desk condition and stability",
        type: "preventive",
        frequency: { type: "years", interval: 1 },
        priority: "low",
        estimatedDuration: 10,
        estimatedCost: 0,
        requiredSkills: ["Facilities"],
        instructions: "Inspect desk for damage, wear, and stability issues.",
        checklistItems: [
          "Check for structural damage",
          "Test drawer functionality",
          "Inspect surface condition",
          "Check stability and wobbling",
          "Verify height adjustment mechanism",
          "Clean and polish surface",
        ],
        triggers: [
          { type: "time_based", value: "annual" },
        ],
        isActive: true,
      },
    ],
    depreciationSettings: {
      method: "straight_line",
      usefulLife: 7,
      usefulLifeUnit: "years",
      salvageValue: 5,
      salvageValueType: "percentage",
      startDate: new Date().toISOString(),
      isActive: true,
    },
    formTemplates: [
      {
        operationType: "asset.create",
        sections: [
          {
            id: "basic-info",
            title: "Basic Information",
            description: "Essential desk details",
            columns: 2,
            fields: ["name", "model", "manufacturer", "serialNumber"],
          },
          {
            id: "specifications",
            title: "Specifications",
            description: "Physical specifications",
            columns: 2,
            fields: ["dimensions", "material", "color", "hasDrawers", "drawerCount", "isAdjustable"],
          },
          {
            id: "purchase-info",
            title: "Purchase Information",
            description: "Financial details",
            columns: 2,
            fields: ["purchasePrice", "purchaseDate", "supplier"],
          },
          {
            id: "location",
            title: "Location",
            description: "Installation location",
            columns: 2,
            fields: ["location", "department", "workspaceId"],
          },
        ],
        settings: {
          layout: "standard",
          labelPosition: "top",
          submitButtonText: "Create Desk",
          cancelButtonText: "Cancel",
          showProgressBar: true,
          allowSaveAsDraft: true,
        },
      },
    ],
  },

  "company-vehicle": {
    id: "company-vehicle",
    name: "Company Vehicle",
    description: "Company vehicle template with automotive-specific configurations",
    category: "Vehicles",
    icon: "Car",
    color: "#EF4444",
    tags: ["Vehicle", "Transportation", "Fleet"],
    assetType: {
      name: "Company Vehicle",
      code: "VEHICLE",
      description: "Company-owned vehicles for business use",
      subcategory: "Automotive",
    },
    customFields: [
      {
        name: "make",
        label: "Make",
        type: "text",
        description: "Vehicle manufacturer",
        isRequired: true,
        isUnique: false,
        validation: { minLength: 2, maxLength: 50 },
        displayOrder: 1,
        groupName: "Vehicle Details",
        isActive: true,
      },
      {
        name: "model",
        label: "Model",
        type: "text",
        description: "Vehicle model",
        isRequired: true,
        isUnique: false,
        validation: { minLength: 1, maxLength: 50 },
        displayOrder: 2,
        groupName: "Vehicle Details",
        isActive: true,
      },
      {
        name: "year",
        label: "Year",
        type: "number",
        description: "Manufacturing year",
        isRequired: true,
        isUnique: false,
        validation: { minValue: 1990, maxValue: new Date().getFullYear() + 1 },
        displayOrder: 3,
        groupName: "Vehicle Details",
        isActive: true,
      },
      {
        name: "vin",
        label: "VIN",
        type: "text",
        description: "Vehicle Identification Number",
        isRequired: true,
        isUnique: true,
        validation: { minLength: 17, maxLength: 17 },
        displayOrder: 4,
        groupName: "Vehicle Details",
        isActive: true,
      },
      {
        name: "licensePlate",
        label: "License Plate",
        type: "text",
        description: "Vehicle license plate number",
        isRequired: true,
        isUnique: true,
        validation: {},
        displayOrder: 5,
        groupName: "Registration",
        isActive: true,
      },
      {
        name: "fuelType",
        label: "Fuel Type",
        type: "select",
        description: "Type of fuel used",
        isRequired: true,
        isUnique: false,
        validation: {},
        options: [
          { value: "Gasoline", label: "Gasoline", isActive: true },
          { value: "Diesel", label: "Diesel", isActive: true },
          { value: "Hybrid", label: "Hybrid", isActive: true },
          { value: "Electric", label: "Electric", isActive: true },
          { value: "CNG", label: "CNG", isActive: true },
        ],
        displayOrder: 6,
        groupName: "Specifications",
        isActive: true,
      },
      {
        name: "mileage",
        label: "Current Mileage",
        type: "number",
        description: "Current odometer reading",
        isRequired: false,
        isUnique: false,
        validation: { minValue: 0 },
        displayOrder: 7,
        groupName: "Status",
        isActive: true,
      },
      {
        name: "assignedDriver",
        label: "Assigned Driver",
        type: "user_select",
        description: "Primary driver assigned to vehicle",
        isRequired: false,
        isUnique: false,
        validation: {},
        displayOrder: 8,
        groupName: "Assignment",
        isActive: true,
      },
    ],
    lifecycleStages: [
      {
        name: "Acquired",
        code: "ACQUIRED",
        description: "Vehicle has been acquired by the company",
        order: 1,
        isInitial: true,
        isFinal: false,
        color: "#10B981",
        icon: "Plus",
        allowedTransitions: ["REGISTERED", "INSPECTION_REQUIRED"],
        requiredFields: ["acquisitionDate", "acquisitionMethod"],
        automatedActions: [
          { type: "create_vehicle_record" }
        ] as AutomatedAction[],
        notifications: ["notify_fleet_manager"],
        isActive: true,
      },
      {
        name: "Registered",
        code: "REGISTERED",
        description: "Vehicle is registered and ready for use",
        order: 2,
        isInitial: false,
        isFinal: false,
        color: "#3B82F6",
        icon: "FileText",
        allowedTransitions: ["ACTIVE", "INSPECTION_REQUIRED"],
        requiredFields: ["registrationDate", "licensePlate", "insurance"],
        automatedActions: [
          { type: "schedule_first_maintenance" }
        ] as AutomatedAction[],
        notifications: [],
        isActive: true,
      },
      {
        name: "Active",
        code: "ACTIVE",
        description: "Vehicle is active and available for use",
        order: 3,
        isInitial: false,
        isFinal: false,
        color: "#059669",
        icon: "Play",
        allowedTransitions: ["MAINTENANCE", "OUT_OF_SERVICE", "DISPOSED"],
        requiredFields: ["assignedDriver"],
        automatedActions: [
          { type: "enable_tracking" },
          { type: "schedule_maintenance" }
        ] as AutomatedAction[],
        notifications: ["vehicle_activated"],
        isActive: true,
      },
      {
        name: "Maintenance",
        code: "MAINTENANCE",
        description: "Vehicle is under maintenance or repair",
        order: 4,
        isInitial: false,
        isFinal: false,
        color: "#F59E0B",
        icon: "Wrench",
        allowedTransitions: ["ACTIVE", "OUT_OF_SERVICE"],
        requiredFields: ["maintenanceType", "serviceProvider"],
        automatedActions: [
          { type: "notify_driver" },
          { type: "arrange_replacement" }
        ] as AutomatedAction[],
        notifications: ["maintenance_started", "maintenance_completed"],
        isActive: true,
      },
      {
        name: "Out of Service",
        code: "OUT_OF_SERVICE",
        description: "Vehicle is temporarily out of service",
        order: 5,
        isInitial: false,
        isFinal: false,
        color: "#EF4444",
        icon: "AlertTriangle",
        allowedTransitions: ["ACTIVE", "DISPOSED"],
        requiredFields: ["outOfServiceReason", "expectedReturnDate"],
        automatedActions: [
          { type: "disable_tracking" },
          { type: "notify_stakeholders" }
        ] as AutomatedAction[],
        notifications: ["vehicle_out_of_service"],
        isActive: true,
      },
      {
        name: "Disposed",
        code: "DISPOSED",
        description: "Vehicle has been sold or disposed of",
        order: 6,
        isInitial: false,
        isFinal: true,
        color: "#7C2D12",
        icon: "Trash2",
        allowedTransitions: [],
        requiredFields: ["disposalMethod", "disposalDate", "finalMileage"],
        automatedActions: [
          { type: "cancel_insurance" },
          { type: "update_fleet_records" }
        ] as AutomatedAction[],
        notifications: ["disposal_completed"],
        isActive: true,
      },
    ],
    maintenanceSchedules: [
      {
        name: "Oil Change",
        description: "Regular engine oil and filter change",
        type: "preventive",
        frequency: { type: "months", interval: 3 },
        priority: "high",
        estimatedDuration: 60,
        estimatedCost: 75,
        requiredSkills: ["Automotive Technician"],
        instructions: "Change engine oil and oil filter according to manufacturer specifications.",
        checklistItems: [
          "Drain old engine oil",
          "Replace oil filter",
          "Add new engine oil",
          "Check oil level",
          "Inspect for leaks",
          "Update maintenance log",
        ],
        triggers: [
          { type: "time_based", value: "quarterly" },
          { type: "mileage_based", value: "5000_miles" },
        ],
        isActive: true,
      },
      {
        name: "Annual Inspection",
        description: "Comprehensive annual vehicle inspection",
        type: "preventive",
        frequency: { type: "years", interval: 1 },
        priority: "high",
        estimatedDuration: 120,
        estimatedCost: 150,
        requiredSkills: ["Certified Inspector"],
        instructions: "Perform comprehensive vehicle safety and emissions inspection.",
        checklistItems: [
          "Check brakes and brake fluid",
          "Inspect tires and tire pressure",
          "Test lights and electrical systems",
          "Check engine performance",
          "Inspect exhaust system",
          "Verify safety equipment",
          "Update inspection certificate",
        ],
        triggers: [
          { type: "time_based", value: "annual" },
          { type: "regulatory", value: "inspection_due" },
        ],
        isActive: true,
      },
    ],
    depreciationSettings: {
      method: "declining_balance",
      usefulLife: 5,
      usefulLifeUnit: "years",
      salvageValue: 15,
      salvageValueType: "percentage",
      startDate: new Date().toISOString(),
      isActive: true,
    },
    formTemplates: [
      {
        operationType: "asset.create",
        sections: [
          {
            id: "vehicle-details",
            title: "Vehicle Details",
            description: "Basic vehicle information",
            columns: 2,
            fields: ["make", "model", "year", "vin"],
          },
          {
            id: "registration",
            title: "Registration",
            description: "Registration and legal information",
            columns: 2,
            fields: ["licensePlate", "registrationDate", "insurancePolicy"],
          },
          {
            id: "specifications",
            title: "Specifications",
            description: "Technical specifications",
            columns: 2,
            fields: ["fuelType", "engineSize", "transmission", "mileage"],
          },
          {
            id: "purchase-info",
            title: "Purchase Information",
            description: "Financial and acquisition details",
            columns: 2,
            fields: ["purchasePrice", "purchaseDate", "dealer", "acquisitionMethod"],
          },
          {
            id: "assignment",
            title: "Assignment",
            description: "Driver and usage assignment",
            columns: 2,
            fields: ["assignedDriver", "primaryUse", "location"],
          },
        ],
        settings: {
          layout: "standard",
          labelPosition: "top",
          submitButtonText: "Register Vehicle",
          cancelButtonText: "Cancel",
          showProgressBar: true,
          allowSaveAsDraft: true,
        },
      },
    ],
  },
};

export class AssetTypeTemplateService {
  /**
   * Get all available templates
   */
  static getTemplates(): AssetTypeTemplate[] {
    return Object.values(ASSET_TYPE_TEMPLATES);
  }

  /**
   * Get template by ID
   */
  static getTemplate(id: string): AssetTypeTemplate | null {
    return ASSET_TYPE_TEMPLATES[id] || null;
  }

  /**
   * Get templates by category
   */
  static getTemplatesByCategory(category: string): AssetTypeTemplate[] {
    return Object.values(ASSET_TYPE_TEMPLATES).filter(
      template => template.category === category
    );
  }

  /**
   * Search templates by tags
   */
  static searchTemplatesByTags(tags: string[]): AssetTypeTemplate[] {
    return Object.values(ASSET_TYPE_TEMPLATES).filter(template =>
      tags.some(tag => template.tags.includes(tag))
    );
  }

  /**
   * Create asset type from template
   */
  static async createAssetTypeFromTemplate(
    templateId: string,
    customizations?: {
      name?: string;
      code?: string;
      description?: string;
      categoryId?: string;
    }
  ): Promise<any> {
    const template = this.getTemplate(templateId);
    if (!template) {
      throw new Error(`Template ${templateId} not found`);
    }

    // This would integrate with the AssetTypeDbService
    // For now, return the template structure
    return {
      ...template.assetType,
      ...customizations,
      customFields: template.customFields,
      lifecycleStages: template.lifecycleStages,
      maintenanceSchedules: template.maintenanceSchedules,
      depreciationSettings: template.depreciationSettings,
      formTemplates: template.formTemplates,
    };
  }

  /**
   * Validate template structure
   */
  static validateTemplate(template: AssetTypeTemplate): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate required fields
    if (!template.name) errors.push("Template name is required");
    if (!template.category) errors.push("Template category is required");
    if (!template.assetType.name) errors.push("Asset type name is required");
    if (!template.assetType.code) errors.push("Asset type code is required");

    // Validate lifecycle stages
    const initialStages = template.lifecycleStages.filter(stage => stage.isInitial);
    if (initialStages.length === 0) {
      warnings.push("No initial lifecycle stage defined");
    } else if (initialStages.length > 1) {
      errors.push("Multiple initial lifecycle stages defined");
    }

    const finalStages = template.lifecycleStages.filter(stage => stage.isFinal);
    if (finalStages.length === 0) {
      warnings.push("No final lifecycle stage defined");
    }

    // Validate custom fields
    template.customFields.forEach((field, index) => {
      if (!field.name) errors.push(`Custom field ${index + 1}: name is required`);
      if (!field.label) errors.push(`Custom field ${index + 1}: label is required`);
      if (!field.type) errors.push(`Custom field ${index + 1}: type is required`);
    });

    // Validate maintenance schedules
    template.maintenanceSchedules.forEach((schedule, index) => {
      if (!schedule.name) errors.push(`Maintenance schedule ${index + 1}: name is required`);
      if (!schedule.type) errors.push(`Maintenance schedule ${index + 1}: type is required`);
      if (!schedule.frequency) errors.push(`Maintenance schedule ${index + 1}: frequency is required`);
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }
}