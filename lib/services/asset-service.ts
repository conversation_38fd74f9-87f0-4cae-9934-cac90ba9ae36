import { prisma } from "@/lib/prisma";
import { Asset, AssetMaintenance, AssetTransfer, AssetDisposal, AssetDepreciation, LeaseAgreement } from "@prisma/client";
import { AssetCreateRequest, AssetUpdateRequest } from "@/lib/types/asset";

export type AssetWithRelations = Asset & {
  maintenances?: AssetMaintenance[];
  transfers?: AssetTransfer[];
  disposals?: AssetDisposal[];
  depreciations?: AssetDepreciation[];
  leases?: LeaseAgreement[];
};

export type AssetCreateInput = AssetCreateRequest;
export type AssetUpdateInput = AssetUpdateRequest;

export class AssetService {
  /**
   * Get all assets with optional relations
   */
  async getAllAssets(includeRelations = false): Promise<AssetWithRelations[]> {
    return prisma.asset.findMany({
      include: includeRelations
        ? {
            maintenances: true,
            transfers: true,
            disposals: true,
            depreciations: true,
            leases: true,
          }
        : undefined,
      orderBy: {
        createdAt: "desc",
      },
    });
  }

  /**
   * Get a single asset by ID with optional relations
   */
  async getAssetById(id: string, includeRelations = false): Promise<AssetWithRelations | null> {
    return prisma.asset.findUnique({
      where: { id },
      include: includeRelations
        ? {
            maintenances: true,
            transfers: true,
            disposals: true,
            depreciations: true,
            leases: true,
          }
        : undefined,
    });
  }

  /**
   * Create a new asset
   */
  async createAsset(data: AssetCreateInput): Promise<Asset> {
    return prisma.asset.create({
      data,
    });
  }

  /**
   * Update an existing asset
   */
  async updateAsset(id: string, data: AssetUpdateInput): Promise<Asset> {
    return prisma.asset.update({
      where: { id },
      data,
    });
  }

  /**
   * Delete an asset
   */
  async deleteAsset(id: string): Promise<Asset> {
    return prisma.asset.delete({
      where: { id },
    });
  }

  /**
   * Get assets by category
   */
  async getAssetsByCategory(category: string): Promise<Asset[]> {
    return prisma.asset.findMany({
      where: { category },
      orderBy: {
        createdAt: "desc",
      },
    });
  }

  /**
   * Get assets by status
   */
  async getAssetsByStatus(status: string): Promise<Asset[]> {
    return prisma.asset.findMany({
      where: { status },
      orderBy: {
        createdAt: "desc",
      },
    });
  }

  /**
   * Get assets by department
   */
  async getAssetsByDepartment(department: string): Promise<Asset[]> {
    return prisma.asset.findMany({
      where: { department },
      orderBy: {
        createdAt: "desc",
      },
    });
  }

  /**
   * Get assets by location
   */
  async getAssetsByLocation(location: string): Promise<Asset[]> {
    return prisma.asset.findMany({
      where: { location },
      orderBy: {
        createdAt: "desc",
      },
    });
  }

  /**
   * Search assets by name, category, or location
   */
  async searchAssets(searchTerm: string): Promise<Asset[]> {
    return prisma.asset.findMany({
      where: {
        OR: [
          { name: { contains: searchTerm, mode: "insensitive" } },
          { category: { contains: searchTerm, mode: "insensitive" } },
          { location: { contains: searchTerm, mode: "insensitive" } },
          { department: { contains: searchTerm, mode: "insensitive" } },
        ],
      },
      orderBy: {
        createdAt: "desc",
      },
    });
  }

  /**
   * Get asset statistics
   */
  async getAssetStatistics() {
    try {
      const totalAssets = await prisma.asset.count();
      const activeAssets = await prisma.asset.count({ where: { status: "active" } });
      const maintenanceAssets = await prisma.asset.count({ where: { status: "maintenance" } });
      const disposedAssets = await prisma.asset.count({ where: { status: "disposed" } });

      // Get recent operations count (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const recentOperations = await prisma.assetOperationHistory.count({
        where: {
          performedAt: {
            gte: thirtyDaysAgo
          }
        }
      });

      const assetsByCategory = await prisma.$queryRaw`
        SELECT category, COUNT(*) as count 
        FROM "Asset" 
        GROUP BY category
      `;

      return {
        totalAssets,
        activeAssets,
        maintenanceAssets,
        disposedAssets,
        recentOperations,
        assetsByCategory,
      };
    } catch (error) {
      console.error('Database connection error in getAssetStatistics:', error);
      // Return mock data for build process
      return {
        totalAssets: 0,
        activeAssets: 0,
        maintenanceAssets: 0,
        disposedAssets: 0,
        recentOperations: 0,
        assetsByCategory: [],
      };
    }
  }
}

// Export a singleton instance
export const assetService = new AssetService();