import { Asset } from "@prisma/client";

// Form data interface for creating assets
export interface AssetFormData {
  name: string;
  category: string;
  serialNumber: string;
  location: string;
  purchasePrice: number;
  purchaseDate: string; // Keep as string for HTML date input
  department: string;
  status: string;
  assetImages: string[];
  assetTypeId: string; // Add assetTypeId
}

// Interface for API request when creating assets
export interface AssetCreateRequest {
  name: string;
  category: string;
  serialNumber: string | null;
  location: string;
  purchasePrice: number;
  purchaseDate: Date;
  department: string | null;
  status: string;
  assetImages: string[];
  assetTypeId: string; // Add assetTypeId
}

// Interface for API request when updating assets
export interface AssetUpdateRequest {
  name?: string;
  category?: string;
  serialNumber?: string | null;
  location?: string;
  purchasePrice?: number;
  purchaseDate?: Date;
  department?: string;
  status?: string;
  assetImages?: string[];
  assetTypeId?: string;
}

// Extended asset type with optional relations
export interface AssetWithRelations extends Asset {
  maintenances?: any[];
  transfers?: any[];
  disposals?: any[];
  depreciations?: any[];
  leases?: any[];
}

// Asset status enum
export enum AssetStatus {
  ACTIVE = "active",
  MAINTENANCE = "maintenance",
  DISPOSED = "disposed",
}

// Asset category enum (you can extend this based on your needs)
export enum AssetCategory {
  IT_EQUIPMENT = "IT Equipment",
  MACHINERY = "Machinery",
  FURNITURE = "Furniture",
  SECURITY = "Security",
  VEHICLES = "Vehicles",
}

// Form validation schema
export const REQUIRED_FIELDS = ["name", "category", "location", "purchasePrice", "purchaseDate", "assetTypeId"] as const;

export type RequiredAssetField = typeof REQUIRED_FIELDS[number];

// Utility function to validate required fields
export function validateAssetFormData(data: AssetFormData): { isValid: boolean; missingFields: RequiredAssetField[] } {
  const missingFields: RequiredAssetField[] = [];
  
  REQUIRED_FIELDS.forEach((field) => {
    if (!data[field] || (field === "purchasePrice" && data[field] <= 0)) {
      missingFields.push(field);
    }
  });
  
  return {
    isValid: missingFields.length === 0,
    missingFields,
  };
}

// Utility function to transform form data to API request format
export function transformAssetFormData(formData: AssetFormData): AssetCreateRequest {
  return {
    name: formData.name.trim(),
    category: formData.category,
    serialNumber: formData.serialNumber.trim() || null,
    location: formData.location.trim(),
    purchasePrice: Number(formData.purchasePrice),
    purchaseDate: new Date(formData.purchaseDate),
    department: formData.department.trim() || null,
    status: formData.status,
    assetImages: formData.assetImages,
    assetTypeId: formData.assetTypeId, // Add assetTypeId
  };
}