"use client";

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Brain, 
  Loader2, 
  Play, 
  BarChart3, 
  Wrench, 
  DollarSign, 
  AlertTriangle,
  CheckCircle,
  MessageSquare
} from 'lucide-react';
import { useAIEngine } from '@/hooks/use-ai-engine';
import { AIAssistant } from '@/components/ai/ai-assistant';
import { AIInsightsDashboard } from '@/components/ai/ai-insights-dashboard';
import { toast } from '@/components/ui/use-toast';

export default function AIDemoPage() {
  const [assetId, setAssetId] = useState('DEMO-001');
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<any>(null);

  const {
    isAnalyzing,
    isPredicting,
    isOptimizing,
    isQuerying,
    error,
    analyzeAsset,
    generateMaintenancePredictions,
    generateCostOptimization,
    processNLPQuery,
    clearError
  } = useAIEngine();

  const handleAnalyzeAsset = async () => {
    clearError();
    
    // Mock asset data for demo
    const mockAssetData = {
      id: assetId,
      name: 'Dell Laptop XPS 15',
      category: 'IT Equipment',
      purchaseDate: '2023-01-15',
      purchasePrice: 1500,
      currentValue: 1200,
      status: 'active',
      location: 'Office Floor 2',
      condition: 'Good',
      utilizationRate: 0.85,
      maintenanceHistory: [
        { date: '2023-12-01', type: 'preventive', cost: 150, description: 'Software update and cleaning' },
        { date: '2023-09-15', type: 'corrective', cost: 300, description: 'Screen replacement' },
        { date: '2023-06-10', type: 'preventive', cost: 50, description: 'Battery calibration' }
      ],
      specifications: {
        processor: 'Intel i7-12700H',
        memory: '16GB DDR4',
        storage: '512GB SSD',
        warranty: '3 years'
      }
    };

    const result = await analyzeAsset(assetId, mockAssetData);
    if (result) {
      setResults({ type: 'analysis', data: result });
      toast({
        title: "Asset Analysis Complete",
        description: `Analysis completed for asset ${assetId}`,
      });
    }
  };

  const handlePredictMaintenance = async () => {
    clearError();
    
    // Mock historical data for demo
    const mockHistoricalData = [
      { date: '2023-12-01', event: 'maintenance', cost: 150, downtime: 2 },
      { date: '2023-09-15', event: 'repair', cost: 300, downtime: 8 },
      { date: '2023-06-10', event: 'inspection', cost: 50, downtime: 0.5 },
      { date: '2023-03-20', event: 'maintenance', cost: 120, downtime: 1.5 },
      { date: '2022-12-15', event: 'repair', cost: 450, downtime: 12 }
    ];

    const result = await generateMaintenancePredictions(assetId, mockHistoricalData);
    if (result) {
      setResults({ type: 'prediction', data: result });
      toast({
        title: "Maintenance Predictions Generated",
        description: `Predictions generated for asset ${assetId}`,
      });
    }
  };

  const handleOptimizeCosts = async () => {
    clearError();
    
    // Mock asset portfolio data for demo
    const mockAssetData = [
      { 
        id: '1', 
        name: 'Dell Laptop XPS 15', 
        category: 'IT Equipment', 
        costs: { maintenance: 1200, operation: 800, depreciation: 500 } 
      },
      { 
        id: '2', 
        name: 'Toyota Forklift', 
        category: 'Machinery', 
        costs: { maintenance: 2500, operation: 1500, depreciation: 1000 } 
      },
      { 
        id: '3', 
        name: 'Conference Table', 
        category: 'Furniture', 
        costs: { maintenance: 300, operation: 100, depreciation: 200 } 
      },
      { 
        id: '4', 
        name: 'HP Printer', 
        category: 'IT Equipment', 
        costs: { maintenance: 800, operation: 400, depreciation: 300 } 
      }
    ];

    const result = await generateCostOptimization(mockAssetData, '1 year');
    if (result) {
      setResults({ type: 'optimization', data: result });
      toast({
        title: "Cost Optimization Complete",
        description: "Cost optimization analysis completed",
      });
    }
  };

  const handleNLPQuery = async () => {
    if (!query.trim()) return;
    
    clearError();
    
    const result = await processNLPQuery(query, { 
      context: 'demo', 
      assetCount: 150, 
      categories: ['IT Equipment', 'Machinery', 'Furniture'] 
    });
    
    if (result) {
      setResults({ type: 'nlp', data: result });
      toast({
        title: "Query Processed",
        description: "Natural language query processed successfully",
      });
    }
  };

  const renderResults = () => {
    if (!results) return null;

    switch (results.type) {
      case 'analysis':
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Asset Analysis Results
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium">Overall Condition</h4>
                  <Badge variant="outline" className="mt-1">
                    {results.data.analysis.condition.status} - {Math.round(results.data.analysis.condition.confidence * 100)}% confidence
                  </Badge>
                </div>
                
                <div>
                  <h4 className="font-medium">Key Insights ({results.data.insights.length})</h4>
                  <ul className="mt-2 space-y-1">
                    {results.data.insights.slice(0, 3).map((insight: any, index: number) => (
                      <li key={index} className="text-sm text-muted-foreground">
                        • {insight.description}
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h4 className="font-medium">Action Items ({results.data.actionItems.length})</h4>
                  <ul className="mt-2 space-y-1">
                    {results.data.actionItems.slice(0, 3).map((item: any, index: number) => (
                      <li key={index} className="text-sm">
                        <Badge variant="outline" className="mr-2 text-xs">
                          {item.priority}
                        </Badge>
                        {item.action}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        );

      case 'prediction':
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wrench className="h-5 w-5" />
                Maintenance Predictions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium">Risk Assessment</h4>
                  <Badge variant="outline" className="mt-1">
                    {results.data.totalRisk} Risk Level
                  </Badge>
                </div>

                <div>
                  <h4 className="font-medium">Predictions ({results.data.predictions.length})</h4>
                  <div className="mt-2 space-y-2">
                    {results.data.predictions.slice(0, 3).map((pred: any, index: number) => (
                      <div key={index} className="p-3 bg-muted rounded-lg">
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium">{pred.component}</p>
                            <p className="text-sm text-muted-foreground">{pred.maintenanceType}</p>
                          </div>
                          <Badge variant="outline">
                            {Math.round(pred.failureProbability * 100)}% risk
                          </Badge>
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                          Est. Cost: ${pred.estimatedCost}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium">Recommendations</h4>
                  <ul className="mt-2 space-y-1">
                    {results.data.recommendations.slice(0, 3).map((rec: string, index: number) => (
                      <li key={index} className="text-sm text-muted-foreground">
                        • {rec}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        );

      case 'optimization':
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Cost Optimization Results
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium">Current Costs</h4>
                    <p className="text-2xl font-bold">${results.data.analysis.currentCosts.toLocaleString()}</p>
                  </div>
                  <div>
                    <h4 className="font-medium">Potential Savings</h4>
                    <p className="text-2xl font-bold text-green-600">
                      ${results.data.analysis.potentialSavings.toLocaleString()}
                    </p>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium">Optimization Opportunities ({results.data.recommendations.length})</h4>
                  <div className="mt-2 space-y-2">
                    {results.data.recommendations.slice(0, 3).map((rec: any, index: number) => (
                      <div key={index} className="p-3 bg-muted rounded-lg">
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium">{rec.action}</p>
                            <p className="text-sm text-muted-foreground">{rec.description}</p>
                          </div>
                          <Badge variant="outline">
                            ${rec.estimatedSavings.toLocaleString()}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2 mt-2">
                          <Badge variant="outline" className="text-xs">
                            {rec.effort} effort
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {rec.timeframe}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        );

      case 'nlp':
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Query Results
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium">Query</h4>
                  <p className="text-sm text-muted-foreground">{results.data.query}</p>
                </div>

                <div>
                  <h4 className="font-medium">Intent & Confidence</h4>
                  <div className="flex gap-2 mt-1">
                    <Badge variant="outline">{results.data.intent}</Badge>
                    <Badge variant="outline">{Math.round(results.data.confidence * 100)}% confidence</Badge>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium">Response</h4>
                  <p className="text-sm bg-muted p-3 rounded-lg">{results.data.response}</p>
                </div>

                {results.data.results.length > 0 && (
                  <div>
                    <h4 className="font-medium">Results ({results.data.results.length})</h4>
                    <div className="mt-2 space-y-1">
                      {results.data.results.slice(0, 3).map((result: any, index: number) => (
                        <div key={index} className="text-sm p-2 bg-muted rounded">
                          {JSON.stringify(result, null, 2)}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        );

      default:
        return null;
    }
  };

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">AI Engine Demo</h1>
          <p className="text-muted-foreground">
            Test and explore AI-powered asset management features
          </p>
        </div>
        <Badge variant="outline" className="flex items-center gap-2">
          <Brain className="h-4 w-4" />
          Powered by Gemini 2.0 Flash
        </Badge>
      </div>

      {error && (
        <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/20">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <p className="text-red-600">{error}</p>
              <Button variant="ghost" size="sm" onClick={clearError}>
                Dismiss
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="functions" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="functions">AI Functions</TabsTrigger>
          <TabsTrigger value="insights">Insights Dashboard</TabsTrigger>
          <TabsTrigger value="assistant">AI Assistant</TabsTrigger>
        </TabsList>

        <TabsContent value="functions" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Asset Analysis */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Asset Analysis
                </CardTitle>
                <CardDescription>
                  Analyze asset condition, performance, and maintenance needs
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="assetId">Asset ID</Label>
                  <Input
                    id="assetId"
                    value={assetId}
                    onChange={(e) => setAssetId(e.target.value)}
                    placeholder="Enter asset ID"
                  />
                </div>
                <Button 
                  onClick={handleAnalyzeAsset} 
                  disabled={isAnalyzing || !assetId}
                  className="w-full"
                >
                  {isAnalyzing ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <Play className="h-4 w-4 mr-2" />
                  )}
                  Analyze Asset
                </Button>
              </CardContent>
            </Card>

            {/* Maintenance Predictions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Wrench className="h-5 w-5" />
                  Maintenance Predictions
                </CardTitle>
                <CardDescription>
                  Generate predictive maintenance recommendations
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="predAssetId">Asset ID</Label>
                  <Input
                    id="predAssetId"
                    value={assetId}
                    onChange={(e) => setAssetId(e.target.value)}
                    placeholder="Enter asset ID"
                  />
                </div>
                <Button 
                  onClick={handlePredictMaintenance} 
                  disabled={isPredicting || !assetId}
                  className="w-full"
                >
                  {isPredicting ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <Play className="h-4 w-4 mr-2" />
                  )}
                  Predict Maintenance
                </Button>
              </CardContent>
            </Card>

            {/* Cost Optimization */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  Cost Optimization
                </CardTitle>
                <CardDescription>
                  Analyze and optimize asset-related costs
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Analyze portfolio of 4 demo assets for cost optimization opportunities
                </p>
                <Button 
                  onClick={handleOptimizeCosts} 
                  disabled={isOptimizing}
                  className="w-full"
                >
                  {isOptimizing ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <Play className="h-4 w-4 mr-2" />
                  )}
                  Optimize Costs
                </Button>
              </CardContent>
            </Card>

            {/* Natural Language Query */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  Natural Language Query
                </CardTitle>
                <CardDescription>
                  Ask questions about your assets in natural language
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="query">Your Question</Label>
                  <Textarea
                    id="query"
                    value={query}
                    onChange={(e) => setQuery(e.target.value)}
                    placeholder="e.g., Show me all assets that need maintenance"
                    rows={3}
                  />
                </div>
                <Button 
                  onClick={handleNLPQuery} 
                  disabled={isQuerying || !query.trim()}
                  className="w-full"
                >
                  {isQuerying ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <Play className="h-4 w-4 mr-2" />
                  )}
                  Process Query
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Results */}
          {results && (
            <div className="space-y-4">
              <Separator />
              <h2 className="text-xl font-semibold">Results</h2>
              {renderResults()}
            </div>
          )}
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <AIInsightsDashboard />
        </TabsContent>

        <TabsContent value="assistant" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>AI Assistant Integration</CardTitle>
              <CardDescription>
                The AI Assistant is available on all pages. Click the chat button in the bottom-right corner to start a conversation.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 bg-muted rounded-lg">
                  <h4 className="font-medium mb-2">Try these example queries:</h4>
                  <ul className="space-y-1 text-sm text-muted-foreground">
                    <li>• "Show me all assets in maintenance"</li>
                    <li>• "Analyze the performance of asset DEMO-001"</li>
                    <li>• "What assets need attention this week?"</li>
                    <li>• "Optimize costs for IT equipment"</li>
                    <li>• "Generate maintenance predictions"</li>
                  </ul>
                </div>
                
                <div className="flex items-center gap-2 p-4 bg-green-50 dark:bg-green-950/20 rounded-lg">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <p className="text-sm">
                    The AI Assistant uses the same Gemini 2.0 Flash model and can access all the AI functions demonstrated above.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* AI Assistant - Always available */}
      <AIAssistant />
    </div>
  );
}