import { AppHeader } from "@/components/layout/app-header";
import type React from "react";
import type { Metadata } from "next";
import { AppSidebar } from "@/components/app-sidebar";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { ErrorBoundary } from "@/components/ui/error-boundary";
import { OfflineIndicator } from "@/components/ui/offline-indicator";
import { Toaster } from "@/components/ui/toaster";

export const metadata: Metadata = {
  title: "WizeAssets - Enterprise Asset Management",
  description: "Advanced ERP platform for comprehensive asset management",
  keywords:
    "asset management, ERP, enterprise, maintenance, financial management",
  authors: [{ name: "WizeAssets Team" }],
  robots: "index, follow",
  openGraph: {
    title: "WizeAssets - Enterprise Asset Management",
    description: "Advanced ERP platform for comprehensive asset management",
    type: "website",
    locale: "en_US",
  },
};

export const viewport = "width=device-width, initial-scale=1"

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ErrorBoundary>
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <AppHeader />
          <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
            <OfflineIndicator />
            {children}
          </div>
        </SidebarInset>
      </SidebarProvider>
      <Toaster />
    </ErrorBoundary>
  );
}
