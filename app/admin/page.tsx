"use client"

import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  TrendingUp,
  TrendingDown,
  Package,
  Wrench,
  CheckCircle,
  Clock,
  DollarSign,
  Users,
  BarChart3,
  Brain,
  Zap,
} from "lucide-react"
import { useAdminHeader } from "@/hooks/use-admin-header"
import { getDashboardHeaderConfig } from "@/lib/utils/admin-header-configs"

export default function Dashboard() {
  // Set up the header for this page
  useAdminHeader(getDashboardHeaderConfig);
  const metrics = [
    {
      title: "Total Assets",
      value: "2,847",
      change: "+12.5%",
      trend: "up",
      icon: Package,
      description: "Active assets in system",
    },
    {
      title: "Maintenance Tasks",
      value: "156",
      change: "-8.2%",
      trend: "down",
      icon: Wrench,
      description: "Pending maintenance",
    },
    {
      title: "System Health",
      value: "94.2%",
      change: "+2.1%",
      trend: "up",
      icon: CheckCircle,
      description: "Overall system status",
    },
    {
      title: "Cost Savings",
      value: "$127K",
      change: "+18.7%",
      trend: "up",
      icon: DollarSign,
      description: "Monthly savings",
    },
  ]

  const recentActivities = [
    {
      id: 1,
      type: "maintenance",
      message: "Scheduled maintenance for Asset #A-2847",
      time: "2 hours ago",
      status: "pending",
    },
    {
      id: 2,
      type: "alert",
      message: "Temperature anomaly detected in Building A",
      time: "4 hours ago",
      status: "critical",
    },
    {
      id: 3,
      type: "completion",
      message: "Preventive maintenance completed for HVAC-001",
      time: "6 hours ago",
      status: "completed",
    },
    { id: 4, type: "assignment", message: "New technician assigned to Sector 7", time: "1 day ago", status: "info" },
  ]

  return (
    <div className="space-y-6">

      {/* Metrics Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {metrics.map((metric, index) => (
          <Card key={index} className="relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10" />
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative">
              <CardTitle className="text-sm font-medium">{metric.title}</CardTitle>
              <metric.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent className="relative">
              <div className="text-2xl font-bold">{metric.value}</div>
              <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                <span className={`flex items-center ${metric.trend === "up" ? "text-green-600" : "text-red-600"}`}>
                  {metric.trend === "up" ? (
                    <TrendingUp className="h-3 w-3 mr-1" />
                  ) : (
                    <TrendingDown className="h-3 w-3 mr-1" />
                  )}
                  {metric.change}
                </span>
                <span>from last month</span>
              </div>
              <p className="text-xs text-muted-foreground mt-1">{metric.description}</p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* System Status */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              System Status
            </CardTitle>
            <CardDescription>Real-time monitoring of critical systems</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">HVAC Systems</span>
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  Operational
                </Badge>
              </div>
              <Progress value={94} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Security Systems</span>
                <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                  Maintenance
                </Badge>
              </div>
              <Progress value={78} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Power Grid</span>
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  Optimal
                </Badge>
              </div>
              <Progress value={98} className="h-2" />
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-blue-500" />
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button variant="outline" className="w-full justify-start">
              <Package className="h-4 w-4 mr-2" />
              Add New Asset
            </Button>
            <Button variant="outline" className="w-full justify-start">
              <Wrench className="h-4 w-4 mr-2" />
              Schedule Maintenance
            </Button>
            <Button variant="outline" className="w-full justify-start">
              <Users className="h-4 w-4 mr-2" />
              Assign Technician
            </Button>
            <Button variant="outline" className="w-full justify-start">
              <BarChart3 className="h-4 w-4 mr-2" />
              Generate Report
            </Button>
            <Button variant="outline" className="w-full justify-start" onClick={() => window.location.href = '/admin/supply-chain'}>
              <Package className="h-4 w-4 mr-2" />
              Supply Chain Dashboard
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activities */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-orange-500" />
            Recent Activities
          </CardTitle>
          <CardDescription>Latest updates and system events</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentActivities.map((activity) => (
              <div key={activity.id} className="flex items-center space-x-4 p-3 rounded-lg border">
                <div
                  className={`h-2 w-2 rounded-full ${
                    activity.status === "critical"
                      ? "bg-red-500"
                      : activity.status === "completed"
                        ? "bg-green-500"
                        : activity.status === "pending"
                          ? "bg-yellow-500"
                          : "bg-blue-500"
                  }`}
                />
                <div className="flex-1 space-y-1">
                  <p className="text-sm font-medium leading-none">{activity.message}</p>
                  <p className="text-xs text-muted-foreground">{activity.time}</p>
                </div>
                <Badge
                  variant={
                    activity.status === "critical"
                      ? "destructive"
                      : activity.status === "completed"
                        ? "secondary"
                        : "outline"
                  }
                >
                  {activity.status}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
