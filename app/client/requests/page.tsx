"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { 
  ShoppingCart, 
  Plus, 
  Search,
  Filter,
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Package,
  Truck,
  Eye,
  MessageSquare,
  DollarSign
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { toast } from "@/components/ui/use-toast";

interface AssetRequest {
  id: string;
  requestNumber: string;
  userId: string;
  assetTypeId?: string;
  assetName: string;
  quantity: number;
  status: "pending" | "approved" | "rejected" | "processing" | "shipped" | "delivered" | "cancelled";
  priority: "low" | "normal" | "high" | "critical";
  justification: string;
  businessCase?: string;
  specifications?: string;
  estimatedCost?: number;
  actualCost?: number;
  budgetCode?: string;
  department?: string;
  location: string;
  expectedDelivery?: string;
  actualDelivery?: string;
  approvedBy?: string;
  approvedAt?: string;
  rejectionReason?: string;
  trackingNumber?: string;
  notes?: string;
  attachments: string[];
  createdAt: string;
  updatedAt: string;
  user?: {
    id: string;
    name: string;
    email: string;
    department?: string;
  };
}

const statusConfig = {
  pending: { 
    label: "Pending Approval", 
    color: "bg-yellow-100 text-yellow-800", 
    icon: Clock,
    description: "Waiting for manager approval"
  },
  approved: { 
    label: "Approved", 
    color: "bg-blue-100 text-blue-800", 
    icon: CheckCircle,
    description: "Request approved, processing order"
  },
  rejected: { 
    label: "Rejected", 
    color: "bg-red-100 text-red-800", 
    icon: XCircle,
    description: "Request was not approved"
  },
  processing: { 
    label: "Processing", 
    color: "bg-purple-100 text-purple-800", 
    icon: Package,
    description: "Order is being prepared"
  },
  shipped: { 
    label: "Shipped", 
    color: "bg-orange-100 text-orange-800", 
    icon: Truck,
    description: "Asset is on the way"
  },
  delivered: { 
    label: "Delivered", 
    color: "bg-green-100 text-green-800", 
    icon: CheckCircle,
    description: "Asset has been delivered"
  },
  cancelled: { 
    label: "Cancelled", 
    color: "bg-gray-100 text-gray-800", 
    icon: XCircle,
    description: "Request was cancelled"
  },
};

const priorityConfig = {
  low: { label: "Low", color: "bg-gray-100 text-gray-800" },
  normal: { label: "Normal", color: "bg-blue-100 text-blue-800" },
  high: { label: "High", color: "bg-orange-100 text-orange-800" },
  critical: { label: "Critical", color: "bg-red-100 text-red-800" },
};

export default function ClientRequestsPage() {
  const router = useRouter();
  const { data: session } = useSession();

  // Calculate progress based on status
  const getProgressFromStatus = (status: string): number => {
    const progressMap: Record<string, number> = {
      pending: 10,
      approved: 30,
      processing: 60,
      shipped: 85,
      delivered: 100,
      rejected: 0,
      cancelled: 0,
    };
    return progressMap[status] || 0;
  };
  const [requests, setRequests] = useState<AssetRequest[]>([]);
  const [filteredRequests, setFilteredRequests] = useState<AssetRequest[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [priorityFilter, setPriorityFilter] = useState("all");

  useEffect(() => {
    loadRequests();
  }, []);

  useEffect(() => {
    filterRequests();
  }, [requests, searchTerm, statusFilter, priorityFilter]);

  const loadRequests = async () => {
    try {
      setIsLoading(true);

      // Fetch asset requests from API
      const response = await fetch('/api/asset-requests?page=1&limit=50&sortBy=createdAt&sortOrder=desc');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch asset requests');
      }

      setRequests(result.data || []);
    } catch (error) {
      console.error("Error loading requests:", error);
      toast({
        title: "Error",
        description: "Failed to load requests. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const filterRequests = () => {
    let filtered = requests;

    if (searchTerm) {
      filtered = filtered.filter(request =>
        request.requestNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.assetName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.justification.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.location.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== "all") {
      filtered = filtered.filter(request => request.status === statusFilter);
    }

    if (priorityFilter !== "all") {
      filtered = filtered.filter(request => request.priority === priorityFilter);
    }

    setFilteredRequests(filtered);
  };

  const getStatusBadge = (status: string) => {
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;
    return (
      <Badge className={config.color}>
        <Icon className="h-3 w-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.normal;
    return (
      <Badge variant="outline" className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const getProgressColor = (status: string) => {
    switch (status) {
      case "rejected":
      case "cancelled":
        return "bg-red-500";
      case "delivered":
        return "bg-green-500";
      case "shipped":
        return "bg-orange-500";
      case "processing":
        return "bg-purple-500";
      case "approved":
        return "bg-blue-500";
      default:
        return "bg-yellow-500";
    }
  };

  const handleCancelRequest = async (requestId: string) => {
    try {
      const updatedRequests = requests.map(request =>
        request.id === requestId
          ? { ...request, status: "cancelled" as const, progress: 0 }
          : request
      );
      
      setRequests(updatedRequests);
      
      toast({
        title: "Request Cancelled",
        description: "Your asset request has been cancelled.",
      });
    } catch (error) {
      console.error("Error cancelling request:", error);
      toast({
        title: "Error",
        description: "Failed to cancel request. Please try again.",
        variant: "destructive",
      });
    }
  };

  const getRequestStats = () => {
    return {
      total: requests.length,
      pending: requests.filter(r => r.status === "pending").length,
      approved: requests.filter(r => r.status === "approved" || r.status === "processing" || r.status === "shipped").length,
      delivered: requests.filter(r => r.status === "delivered").length,
      rejected: requests.filter(r => r.status === "rejected").length,
    };
  };

  const stats = getRequestStats();

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2">Loading requests...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">My Asset Requests</h1>
          <p className="text-muted-foreground">
            Track and manage your asset requests
          </p>
        </div>
        <Button onClick={() => router.push("/client/requests/new")}>
          <Plus className="h-4 w-4 mr-2" />
          New Request
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-5">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">Total Requests</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
            <p className="text-xs text-muted-foreground">Pending</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">{stats.approved}</div>
            <p className="text-xs text-muted-foreground">In Progress</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">{stats.delivered}</div>
            <p className="text-xs text-muted-foreground">Delivered</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-red-600">{stats.rejected}</div>
            <p className="text-xs text-muted-foreground">Rejected</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <div className="flex-1 max-w-md">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search requests..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="processing">Processing</SelectItem>
                <SelectItem value="shipped">Shipped</SelectItem>
                <SelectItem value="delivered">Delivered</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priority</SelectItem>
                <SelectItem value="low">Low</SelectItem>
                <SelectItem value="normal">Normal</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="critical">Critical</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Requests List */}
      <div className="space-y-4">
        {filteredRequests.map((request) => (
          <Card key={request.id}>
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-start gap-4">
                  <div className="p-2 bg-muted rounded-lg">
                    <Package className="h-5 w-5" />
                  </div>
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-semibold">{request.assetName}</h3>
                      {getStatusBadge(request.status)}
                      {getPriorityBadge(request.priority)}
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">
                      {request.requestNumber} • Quantity: {request.quantity}
                    </p>
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        Requested: {new Date(request.createdAt).toLocaleDateString()}
                      </div>
                      {request.expectedDelivery && (
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          Expected: {new Date(request.expectedDelivery).toLocaleDateString()}
                        </div>
                      )}
                      <div className="flex items-center gap-1">
                        <DollarSign className="h-3 w-3" />
                        ${request.estimatedCost?.toLocaleString() || 'TBD'}
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </Button>
                  {request.trackingNumber && (
                    <Button variant="outline" size="sm">
                      <Truck className="h-4 w-4 mr-1" />
                      Track
                    </Button>
                  )}
                  {request.status === "pending" && (
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleCancelRequest(request.id)}
                    >
                      Cancel
                    </Button>
                  )}
                  <Button variant="outline" size="sm">
                    <MessageSquare className="h-4 w-4 mr-1" />
                    Support
                  </Button>
                </div>
              </div>

              {/* Progress Bar */}
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Progress</span>
                  <span>{getProgressFromStatus(request.status)}%</span>
                </div>
                <Progress
                  value={getProgressFromStatus(request.status)}
                  className="h-2"
                />
                <p className="text-xs text-muted-foreground">
                  {statusConfig[request.status as keyof typeof statusConfig]?.description}
                </p>
              </div>

              {/* Additional Info */}
              <div className="mt-4 pt-4 border-t">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Location:</span>
                    <p className="font-medium">{request.location}</p>
                  </div>
                  {request.department && (
                    <div>
                      <span className="text-muted-foreground">Department:</span>
                      <p className="font-medium">{request.department}</p>
                    </div>
                  )}
                  {request.approvedBy && (
                    <div>
                      <span className="text-muted-foreground">Approved By:</span>
                      <p className="font-medium">{request.approvedBy}</p>
                    </div>
                  )}
                </div>
                
                {request.rejectionReason && (
                  <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex items-start gap-2">
                      <AlertCircle className="h-4 w-4 text-red-600 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-red-800">Rejection Reason</p>
                        <p className="text-sm text-red-700">{request.rejectionReason}</p>
                      </div>
                    </div>
                  </div>
                )}
                
                {request.trackingNumber && (
                  <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Truck className="h-4 w-4 text-blue-600" />
                      <div>
                        <p className="text-sm font-medium text-blue-800">Tracking Number</p>
                        <p className="text-sm text-blue-700 font-mono">{request.trackingNumber}</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}

        {filteredRequests.length === 0 && (
          <Card>
            <CardContent className="p-8 text-center">
              <ShoppingCart className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Requests Found</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm || statusFilter !== "all" || priorityFilter !== "all"
                  ? "No requests match your current filters."
                  : "You haven't submitted any asset requests yet."
                }
              </p>
              <div className="flex items-center justify-center gap-2">
                {(searchTerm || statusFilter !== "all" || priorityFilter !== "all") && (
                  <Button 
                    variant="outline" 
                    onClick={() => {
                      setSearchTerm("");
                      setStatusFilter("all");
                      setPriorityFilter("all");
                    }}
                  >
                    Clear Filters
                  </Button>
                )}
                <Button onClick={() => router.push("/client/requests/new")}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Request
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}