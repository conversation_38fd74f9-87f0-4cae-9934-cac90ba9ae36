import { NextRequest, NextResponse } from "next/server";
import { assetService } from "@/lib/services/asset-service";

export async function GET(request: NextRequest) {
  try {
    const statistics = await assetService.getAssetStatistics();
    return NextResponse.json(statistics);
  } catch (error) {
    console.error("Error fetching asset statistics:", error);
    return NextResponse.json(
      { error: "Failed to fetch asset statistics" },
      { status: 500 }
    );
  }
}