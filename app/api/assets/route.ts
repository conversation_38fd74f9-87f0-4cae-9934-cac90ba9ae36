import { NextRequest, NextResponse } from "next/server";
import { assetService } from "@/lib/services/asset-service";

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const category = searchParams.get("category");
    const status = searchParams.get("status");
    const department = searchParams.get("department");
    const location = searchParams.get("location");
    const search = searchParams.get("search");
    const includeRelations = searchParams.get("includeRelations") === "true";

    let assets;

    if (search) {
      assets = await assetService.searchAssets(search);
    } else if (category) {
      assets = await assetService.getAssetsByCategory(category);
    } else if (status) {
      assets = await assetService.getAssetsByStatus(status);
    } else if (department) {
      assets = await assetService.getAssetsByDepartment(department);
    } else if (location) {
      assets = await assetService.getAssetsByLocation(location);
    } else {
      assets = await assetService.getAllAssets(includeRelations);
    }

    return NextResponse.json(assets);
  } catch (error) {
    console.error("Error fetching assets:", error);
    return NextResponse.json(
      { error: "Failed to fetch assets" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    // Transform the data to match schema requirements
    const transformedData = {
      ...data,
      purchaseDate: data.purchaseDate ? new Date(data.purchaseDate) : new Date(),
      purchasePrice: Number(data.purchasePrice),
      serialNumber: data.serialNumber || null,
      department: data.department || null,
      assetImages: data.assetImages || [],
    };
    
    const asset = await assetService.createAsset(transformedData);
    return NextResponse.json(asset, { status: 201 });
  } catch (error) {
    console.error("Error creating asset:", error);
    return NextResponse.json(
      { error: "Failed to create asset" },
      { status: 500 }
    );
  }
}